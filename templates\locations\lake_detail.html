{% extends 'base.html' %}
{% load static %}

{% block title %}{{ lake.name }} - Răsfățul Pescarului{% endblock %}

{% block external_css %}
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
<style>
.map-container {
    width: 100%;
    height: 450px;
    border-radius: 0.5rem;
    overflow: hidden;
}
.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
}
</style>
{% endblock %}

{% block content %}
<div class="container py-5 mt-5">
    <nav aria-label="breadcrumb">
        <ol class="breadcrumb">
            <li class="breadcrumb-item"><a href="{% url 'main:home' %}">Acasă</a></li>
            <li class="breadcrumb-item"><a href="{% url 'main:locations_map' %}">B<PERSON><PERSON><PERSON><PERSON> de pescuit</a></li>
            <li class="breadcrumb-item"><a href="{% url 'main:county_lakes' lake.county.slug %}">{{ lake.county.name }}</a></li>
            <li class="breadcrumb-item active" aria-current="page">{{ lake.name }}</li>
        </ol>
    </nav>

    <div class="row">
        <!-- Lake Gallery and Details -->
        <div class="col-lg-8">
            <div class="card shadow-sm mb-4">
                <!-- Gallery Section -->
                {% if lake.has_gallery %}
                    {% with gallery_photos=lake.get_gallery_photos %}
                    <div class="lake-gallery">
                        <!-- Main Photo -->
                        <div class="main-photo-container">
                            {% with main_photo=lake.get_main_photo %}
                            <img src="{{ main_photo.url }}"
                                 alt="{{ lake.name }}"
                                 class="card-img-top main-photo"
                                 data-bs-toggle="modal"
                                 data-bs-target="#galleryModal"
                                 data-photo-index="0"
                                 style="cursor: pointer;">
                            {% endwith %}

                            <!-- Photo Counter -->
                            {% if gallery_photos|length > 1 %}
                            <div class="photo-counter">
                                <i class="fas fa-images me-1"></i>
                                <span id="current-photo">1</span> / {{ gallery_photos|length }}
                            </div>
                            {% endif %}
                        </div>

                        <!-- Thumbnail Navigation (if more than 1 photo) -->
                        {% if gallery_photos|length > 1 %}
                        <div class="thumbnail-nav mt-3">
                            <div class="row g-2">
                                {% for photo in gallery_photos %}
                                <div class="col-2">
                                    <img src="{{ photo.image.url }}"
                                         alt="{{ lake.name }} - Foto {{ forloop.counter }}"
                                         class="img-fluid thumbnail {% if forloop.first %}active{% endif %}"
                                         data-photo-index="{{ forloop.counter0 }}"
                                         data-bs-toggle="modal"
                                         data-bs-target="#galleryModal"
                                         style="cursor: pointer; border-radius: 0.25rem; aspect-ratio: 1; object-fit: cover;">
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% endwith %}
                {% else %}
                    <!-- Fallback to legacy image or placeholder -->
                    {% if lake.image %}
                    <img src="{{ lake.image.url }}" alt="{{ lake.name }}" class="card-img-top">
                    {% else %}
                    <img src="{% static 'images/lake-placeholder.jpg' %}" alt="{{ lake.name }}" class="card-img-top">
                    {% endif %}
                {% endif %}
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start mb-3">
                        <h1 class="card-title h2 mb-0">{{ lake.name }}</h1>
                        {% if user.is_authenticated and lake.can_edit:user %}
                            <div class="dropdown">
                                <button class="btn btn-outline-secondary btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cog"></i>
                                </button>
                                <ul class="dropdown-menu">
                                    <li><a class="dropdown-item" href="{% url 'main:editeaza_balta' lake.slug %}">
                                        <i class="fas fa-edit me-2"></i>Editează
                                    </a></li>
                                    <li><a class="dropdown-item" href="{% url 'main:manage_lake_photos' lake.slug %}">
                                        <i class="fas fa-images me-2"></i>Gestionează fotografii
                                    </a></li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li><a class="dropdown-item text-danger" href="{% url 'main:sterge_balta' lake.slug %}">
                                        <i class="fas fa-trash me-2"></i>Șterge
                                    </a></li>
                                </ul>
                            </div>
                        {% endif %}
                    </div>
                    <p class="text-muted mb-3">
                        <i class="fas fa-map-marker-alt me-2"></i>{{ lake.address }}
                    </p>
                    <div class="lake-description mb-4">
                        {{ lake.description|linebreaks }}
                    </div>

                    <!-- Fish Species -->
                    <div class="mb-4">
                        <h5 class="mb-3">Specii de pești</h5>
                        <div class="d-flex flex-wrap gap-2">
                            {% for fish in lake.fish_species.all %}
                            <span class="badge" style="background-color: #198754 !important;">
                                <i class="fas fa-fish me-1"></i>{{ fish.name }}
                            </span>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- Facilities -->
                    <div class="mb-4">
                        <h5 class="mb-3">Facilități</h5>
                        {% regroup lake.facilities.all by category as facility_groups %}
                        {% for group in facility_groups %}
                        <div class="mb-3">
                            <h6 class="text-muted mb-2">{{ group.grouper|capfirst }}</h6>
                            <div class="d-flex flex-wrap gap-2">
                                {% for facility in group.list %}
                                <div class="facility-item">
                                    <i class="{{ facility.icon_class }} text-success"></i>
                                    <span>{{ facility.name }}</span>
                                </div>
                                {% endfor %}
                            </div>
                        </div>
                        {% endfor %}
                    </div>

                    <!-- Lake Type -->
                    <div class="mb-4">
                        <h5 class="mb-3">Tipul bălții</h5>
                        <span class="badge fs-6" style="background-color: #198754;">
                            <i class="fas fa-tag me-2"></i>{{ lake.get_lake_type_display }}
                        </span>
                    </div>

                    <!-- Rating -->
                    {% if lake.total_reviews > 0 %}
                    <div class="mb-4">
                        <h5 class="mb-3">Rating</h5>
                        <div class="d-flex align-items-center gap-3">
                            <div class="rating-stars">
                                {% for i in "12345" %}
                                    {% if forloop.counter <= lake.average_rating %}
                                        <i class="fas fa-star" style="color: #ffc107;"></i>
                                    {% else %}
                                        <i class="far fa-star" style="color: #e9ecef;"></i>
                                    {% endif %}
                                {% endfor %}
                            </div>
                            <span class="fw-bold">{{ lake.average_rating }}</span>
                            <span class="text-muted">({{ lake.total_reviews }} recenzi{% if lake.total_reviews == 1 %}e{% else %}i{% endif %})</span>
                        </div>
                    </div>
                    {% endif %}

                    <!-- Price -->
                    <div class="mb-4">
                        <h5 class="mb-3">Preț</h5>
                        <p class="h4 text-success">
                            <i class="fas fa-coins me-2"></i>{{ lake.price_per_day }} Lei/zi
                        </p>
                    </div>

                    <!-- Operating Hours -->
                    {% if lake.operating_hours %}
                    <div class="mb-4">
                        <h5 class="mb-3">Program de funcționare</h5>
                        <div class="operating-hours-content">
                            <i class="fas fa-clock me-2"></i>
                            <div class="table-responsive">
                                <table class="table table-sm mb-0">
                                    <tbody>
                                        <tr>
                                            <td><strong>Luni</strong></td>
                                            <td>
                                                {% if lake.operating_hours.monday_is_open %}
                                                    {% if lake.operating_hours.monday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.monday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.monday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.monday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.monday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Marți</strong></td>
                                            <td>
                                                {% if lake.operating_hours.tuesday_is_open %}
                                                    {% if lake.operating_hours.tuesday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.tuesday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.tuesday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.tuesday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.tuesday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Miercuri</strong></td>
                                            <td>
                                                {% if lake.operating_hours.wednesday_is_open %}
                                                    {% if lake.operating_hours.wednesday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.wednesday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.wednesday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.wednesday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.wednesday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Joi</strong></td>
                                            <td>
                                                {% if lake.operating_hours.thursday_is_open %}
                                                    {% if lake.operating_hours.thursday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.thursday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.thursday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.thursday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.thursday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Vineri</strong></td>
                                            <td>
                                                {% if lake.operating_hours.friday_is_open %}
                                                    {% if lake.operating_hours.friday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.friday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.friday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.friday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.friday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Sâmbătă</strong></td>
                                            <td>
                                                {% if lake.operating_hours.saturday_is_open %}
                                                    {% if lake.operating_hours.saturday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.saturday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.saturday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.saturday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.saturday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                        <tr>
                                            <td><strong>Duminică</strong></td>
                                            <td>
                                                {% if lake.operating_hours.sunday_is_open %}
                                                    {% if lake.operating_hours.sunday_is_24h %}
                                                        <span class="badge bg-success">24 ore</span>
                                                    {% else %}
                                                        {{ lake.operating_hours.sunday_opening_time|time:"H:i"|default:"--" }} - {{ lake.operating_hours.sunday_closing_time|time:"H:i"|default:"--" }}
                                                    {% endif %}
                                                    {% if lake.operating_hours.sunday_special_notes %}
                                                        <small class="text-muted d-block">{{ lake.operating_hours.sunday_special_notes }}</small>
                                                    {% endif %}
                                                {% else %}
                                                    <span class="badge bg-danger">Închis</span>
                                                {% endif %}
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            {% if lake.operating_hours.general_notes %}
                            <div class="mt-2">
                                <small class="text-muted">{{ lake.operating_hours.general_notes|linebreaks }}</small>
                            </div>
                            {% endif %}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Rules -->
                    {% if lake.rules %}
                    <div class="mb-4">
                        <h5 class="mb-3">Reguli</h5>
                        <div class="rules-content">
                            {{ lake.rules|linebreaks }}
                        </div>
                    </div>
                    {% endif %}

                    <!-- Navigation Button -->
                    <div class="d-grid">
                        <a href="https://www.google.com/maps/dir/?api=1&destination={{ lake.latitude }},{{ lake.longitude }}"
                           class="btn btn-success" target="_blank">
                            <i class="fab fa-google me-2"></i>Navigare cu Google Maps
                        </a>
                    </div>
                </div>
            </div>

            <!-- Map -->
            <div class="card shadow-sm mb-4">
                <div class="card-body">
                    <h5 class="card-title mb-3">Locație</h5>
                    <div class="map-container">
                        {% if lake.get_safe_google_maps_embed %}
                            <!-- Custom Google Maps embed -->
                            {{ lake.get_safe_google_maps_embed }}
                        {% else %}
                            <!-- Default coordinate-based map -->
                            <iframe
                                src="https://maps.google.com/maps?q={{ lake.latitude }},{{ lake.longitude }}&hl=ro&z=15&output=embed"
                                style="border:0;"
                                allowfullscreen=""
                                loading="lazy"
                                referrerpolicy="no-referrer-when-downgrade">
                            </iframe>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Nearby Lakes -->
        <div class="col-lg-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-3">Bălți în apropiere</h5>
                    {% if nearby_lakes %}
                    {% for nearby in nearby_lakes %}
                    <div class="mb-3">
                        <a href="{% url 'main:lake_detail' nearby.slug %}" class="text-decoration-none">
                            <div class="ratio ratio-16x9 mb-2">
                                {% with display_image=nearby.get_display_image %}
                                {% if display_image %}
                                <img src="{{ display_image.url }}" alt="{{ nearby.name }}" class="img-fluid rounded">
                                {% else %}
                                <img src="{% static 'images/lake-placeholder.jpg' %}" alt="{{ nearby.name }}" class="img-fluid rounded">
                                {% endif %}
                                {% endwith %}
                            </div>
                            <h6 class="mb-1">{{ nearby.name }}</h6>
                        </a>
                        <small class="text-muted">{{ nearby.location }}</small>
                    </div>
                    {% if not forloop.last %}
                    <hr>
                    {% endif %}
                    {% endfor %}
                    {% else %}
                    <p class="text-muted">Nu există bălți în apropiere.</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- Reviews Section -->
    <div class="row mt-5">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h5 class="card-title mb-4">Recenzii</h5>

                    {% if lake.reviews.all %}
                    <div class="reviews-list">
                        {% for review in lake.reviews.all %}
                        {% if review.is_approved and not review.is_spam %}
                        <div class="review-card mb-4 p-3 border rounded">
                            <div class="d-flex justify-content-between align-items-start mb-2">
                                <div>
                                    <h6 class="mb-1">{{ review.title }}</h6>
                                    <div class="d-flex align-items-center gap-2 mb-1">
                                        <div class="rating-stars">
                                            {% for i in "12345" %}
                                                {% if forloop.counter <= review.rating %}
                                                    <i class="fas fa-star" style="color: #ffc107;"></i>
                                                {% else %}
                                                    <i class="far fa-star" style="color: #e9ecef;"></i>
                                                {% endif %}
                                            {% endfor %}
                                        </div>
                                        <span class="fw-bold">{{ review.rating }}/5</span>
                                    </div>
                                    <small class="text-muted">de {{ review.reviewer_name }} • {{ review.visit_date|date:"d M Y" }}</small>
                                </div>
                                <small class="text-muted">{{ review.created_at|date:"d M Y" }}</small>
                            </div>
                            <p class="mb-0">{{ review.comment }}</p>
                        </div>
                        {% endif %}
                        {% endfor %}
                    </div>
                    {% else %}
                    <p class="text-muted text-center py-4">Nu există recenzii pentru acest lac încă. Fii primul care lasă o recenzie!</p>
                    {% endif %}

                    <!-- Add Review Button -->
                    <div class="text-center mt-4">
                        <button class="btn btn-success" data-bs-toggle="modal" data-bs-target="#reviewModal">
                            <i class="fas fa-plus me-2"></i>Adaugă o recenzie
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Social Sharing & Last Updated -->
    <div class="row mt-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h6 class="mb-3"><i class="fas fa-share-alt me-2"></i>Partajează această baltă</h6>
                            <div class="social-sharing d-flex gap-2 flex-wrap">
                                <a href="https://www.facebook.com/sharer/sharer.php?u={{ request.build_absolute_uri }}"
                                   target="_blank"
                                   class="btn btn-outline-primary btn-sm social-btn">
                                    <i class="fab fa-facebook-f me-1"></i>Facebook
                                </a>
                                <a href="https://twitter.com/intent/tweet?url={{ request.build_absolute_uri }}&text=Descoperă%20{{ lake.name|urlencode }}%20-%20o%20baltă%20de%20pescuit%20în%20{{ lake.address|urlencode }}"
                                   target="_blank"
                                   class="btn btn-outline-info btn-sm social-btn">
                                    <i class="fab fa-twitter me-1"></i>Twitter
                                </a>
                                <a href="https://wa.me/?text=Descoperă%20{{ lake.name|urlencode }}%20-%20o%20baltă%20de%20pescuit%20în%20{{ lake.address|urlencode }}%20{{ request.build_absolute_uri }}"
                                   target="_blank"
                                   class="btn btn-outline-success btn-sm social-btn">
                                    <i class="fab fa-whatsapp me-1"></i>WhatsApp
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={{ request.build_absolute_uri }}"
                                   target="_blank"
                                   class="btn btn-outline-primary btn-sm social-btn">
                                    <i class="fab fa-linkedin-in me-1"></i>LinkedIn
                                </a>
                                <button class="btn btn-outline-secondary btn-sm social-btn" onclick="copyToClipboard('{{ request.build_absolute_uri }}')">
                                    <i class="fas fa-copy me-1"></i>Copiază link
                                </button>
                            </div>
                        </div>
                        <div class="col-md-6 text-md-end">
                            <div class="last-updated">
                                <small class="text-muted">
                                    <i class="fas fa-clock me-1"></i>
                                    Ultima actualizare: {{ lake.get_last_updated_display }}
                                </small>
                                {% if lake.owner %}
                                    <br>
                                    <small class="text-muted">
                                        <i class="fas fa-user me-1"></i>
                                        Adăugat de: {{ lake.owner.get_full_name|default:lake.owner.username }}
                                    </small>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Review Modal -->
<div class="modal fade" id="reviewModal" tabindex="-1" aria-labelledby="reviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="reviewModalLabel">Adaugă o recenzie pentru {{ lake.name }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form method="post" action="{% url 'main:add_review' lake.id %}">
                <div class="modal-body">
                    {% csrf_token %}
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="reviewer_name" class="form-label">Numele dvs. *</label>
                            <input type="text" class="form-control" id="reviewer_name" name="reviewer_name" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="reviewer_email" class="form-label">Email *</label>
                            <input type="email" class="form-control" id="reviewer_email" name="reviewer_email" required>
                            <div class="form-text">Nu va fi afișat public</div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="rating" class="form-label">Rating *</label>
                            <select class="form-select" id="rating" name="rating" required>
                                <option value="">Selectează rating</option>
                                <option value="5">⭐⭐⭐⭐⭐ (5 stele)</option>
                                <option value="4">⭐⭐⭐⭐ (4 stele)</option>
                                <option value="3">⭐⭐⭐ (3 stele)</option>
                                <option value="2">⭐⭐ (2 stele)</option>
                                <option value="1">⭐ (1 stea)</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label for="visit_date" class="form-label">Data vizitei *</label>
                            <input type="date" class="form-control" id="visit_date" name="visit_date" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="title" class="form-label">Titlul recenziei *</label>
                        <input type="text" class="form-control" id="title" name="title" maxlength="200" required>
                        <div class="form-text">Titlu scurt pentru experiența dvs.</div>
                    </div>
                    <div class="mb-3">
                        <label for="comment" class="form-label">Comentariu *</label>
                        <textarea class="form-control" id="comment" name="comment" rows="4" maxlength="1000" required></textarea>
                        <div class="form-text">Descrieți experiența dvs. la acest lac (minim 20 caractere)</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Anulează</button>
                    <button type="submit" class="btn btn-success">Trimite recenzia</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Gallery Lightbox Modal -->
{% if lake.has_gallery %}
<div class="modal fade" id="galleryModal" tabindex="-1" aria-hidden="true" data-bs-backdrop="false">
    <div class="modal-dialog modal-fullscreen p-0">
        <div class="modal-content border-0" style="background: rgba(0, 0, 0, 0.9);">
            <div class="modal-body p-0 position-relative gallery-modal-body">
                <!-- Close Button -->
                <button type="button" class="btn-close btn-close-white gallery-close" data-bs-dismiss="modal" aria-label="Close">✕</button>

                <!-- Main Gallery Image -->
                <div class="gallery-main-container" id="gallery-main-container">
                    <img id="gallery-main-image"
                         src=""
                         alt="{{ lake.name }}"
                         class="img-fluid gallery-image"
                         style="object-fit: contain; max-height: 90vh; max-width: 90vw;">
                </div>

                <!-- Navigation Arrows -->
                {% with gallery_photos=lake.get_gallery_photos %}
                {% if gallery_photos|length > 1 %}
                <button class="btn btn-dark gallery-nav gallery-prev" type="button">
                    <i class="fas fa-chevron-left"></i>
                </button>
                <button class="btn btn-dark gallery-nav gallery-next" type="button">
                    <i class="fas fa-chevron-right"></i>
                </button>
                {% endif %}

                <!-- Photo Counter -->
                <div class="gallery-counter">
                    <span id="gallery-current">1</span> / {{ gallery_photos|length }}
                </div>
                {% endwith %}
            </div>
        </div>
    </div>
</div>
{% endif %}

{% block external_js %}{% endblock %}

{% block extra_js %}

<style>
/* Gallery Styles */
.lake-gallery {
    position: relative;
}

.main-photo-container {
    position: relative;
    overflow: hidden;
    border-radius: 0.375rem 0.375rem 0 0;
}

.main-photo {
    transition: transform 0.3s ease;
    width: 100%;
    height: 400px;
    object-fit: cover;
}

.main-photo:hover {
    transform: scale(1.05);
}

.photo-counter {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 1rem;
    font-size: 0.875rem;
    font-weight: 500;
}

.thumbnail-nav .thumbnail {
    border: 2px solid transparent;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.thumbnail-nav .thumbnail:hover,
.thumbnail-nav .thumbnail.active {
    border-color: #198754;
    opacity: 1;
    transform: scale(1.05);
}

/* Lightbox Modal Styles */
.gallery-main-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100vh;
    width: 100vw;
    cursor: pointer;
}

.gallery-image {
    cursor: default;
}

.gallery-close {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 1070;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7) !important;
    border: none;
    font-size: 1.2rem;
    font-weight: bold;
    color: white !important;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.gallery-close:hover {
    background: rgba(0, 0, 0, 0.9) !important;
    transform: scale(1.1);
    color: white !important;
}

.gallery-close:focus {
    box-shadow: 0 0 0 0.25rem rgba(255, 255, 255, 0.25);
    color: white !important;
}

.gallery-nav {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1060;
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(0, 0, 0, 0.7) !important;
    border: none;
    color: white;
    font-size: 1.5rem;
    transition: all 0.3s ease;
}

.gallery-nav:hover {
    background: rgba(0, 0, 0, 0.9) !important;
    color: white;
    transform: translateY(-50%) scale(1.1);
}

.gallery-prev {
    left: 2rem;
}

.gallery-next {
    right: 2rem;
}

.gallery-counter {
    position: absolute;
    bottom: 2rem;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-size: 1rem;
    font-weight: 500;
    z-index: 1060;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .main-photo {
        height: 250px;
    }

    .gallery-nav {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .gallery-prev {
        left: 1rem;
    }

    .gallery-next {
        right: 1rem;
    }

    .gallery-close {
        width: 40px;
        height: 40px;
        font-size: 1rem;
        top: 0.5rem;
        right: 0.5rem;
    }

    .gallery-counter {
        bottom: 1rem;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}
.facility-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: #f8f9fa;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
}

.facility-item i {
    font-size: 1.2rem;
}

.rules-content {
    background: #f8f9fa;
    padding: 1rem;
    border-radius: 0.5rem;
}

.operating-hours-content {
    background: #e8f5e8;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid #198754;
}

.map-container iframe {
    width: 100%;
    height: 100%;
    border: 0;
    border-radius: 0.5rem;
}

.review-card {
    background: #f8f9fa;
    transition: all 0.3s ease;
}

.review-card:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.rating-stars i {
    font-size: 1rem;
}

.operating-hours-content table {
    background: white;
    border-radius: 0.25rem;
}

.operating-hours-content table tr:nth-child(even) {
    background-color: #f8f9fa;
}

/* Social Sharing Styles */
.social-sharing {
    margin-bottom: 1rem;
}

.social-btn {
    transition: all 0.3s ease;
    border-radius: 20px;
    font-weight: 500;
}

.social-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.last-updated {
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #198754;
}

@media (max-width: 768px) {
    .social-sharing {
        justify-content: center;
    }

    .last-updated {
        text-align: center !important;
        margin-top: 1rem;
    }
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Gallery functionality
    const galleryPhotos = [
        {% if lake.has_gallery %}
        {% for photo in lake.get_gallery_photos %}
        {
            url: "{{ photo.image.url }}",
            alt: "{{ lake.name }} - Foto {{ forloop.counter }}"
        }{% if not forloop.last %},{% endif %}
        {% endfor %}
        {% endif %}
    ];

    let currentPhotoIndex = 0;

    // Thumbnail click handlers
    document.querySelectorAll('.thumbnail').forEach((thumbnail, index) => {
        thumbnail.addEventListener('click', function() {
            currentPhotoIndex = parseInt(this.dataset.photoIndex);
            updateMainPhoto();
            updateThumbnailActive();
        });
    });

    // Main photo click handler
    const mainPhoto = document.querySelector('.main-photo');
    if (mainPhoto) {
        mainPhoto.addEventListener('click', function() {
            currentPhotoIndex = parseInt(this.dataset.photoIndex);
            updateGalleryModal();
        });
    }

    // Gallery modal navigation
    const prevBtn = document.querySelector('.gallery-prev');
    const nextBtn = document.querySelector('.gallery-next');

    if (prevBtn) {
        prevBtn.addEventListener('click', function() {
            currentPhotoIndex = currentPhotoIndex > 0 ? currentPhotoIndex - 1 : galleryPhotos.length - 1;
            updateGalleryModal();
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', function() {
            currentPhotoIndex = currentPhotoIndex < galleryPhotos.length - 1 ? currentPhotoIndex + 1 : 0;
            updateGalleryModal();
        });
    }

    // Keyboard navigation
    document.addEventListener('keydown', function(e) {
        const modal = document.getElementById('galleryModal');
        if (modal && modal.classList.contains('show')) {
            if (e.key === 'ArrowLeft') {
                e.preventDefault();
                if (prevBtn) prevBtn.click();
            } else if (e.key === 'ArrowRight') {
                e.preventDefault();
                if (nextBtn) nextBtn.click();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                bootstrap.Modal.getInstance(modal).hide();
            }
        }
    });

    // Click outside to close functionality
    const galleryModalBody = document.querySelector('.gallery-modal-body');
    if (galleryModalBody) {
        galleryModalBody.addEventListener('click', function(e) {
            // Check if click is on the background (not on image, arrows, counter, or close button)
            if (e.target === galleryModalBody || e.target.id === 'gallery-main-container') {
                const modal = bootstrap.Modal.getInstance(document.getElementById('galleryModal'));
                if (modal) {
                    modal.hide();
                }
            }
        });
    }

    // Touch/swipe support for mobile
    let touchStartX = 0;
    let touchEndX = 0;

    const galleryContainer = document.querySelector('.gallery-main-container');
    if (galleryContainer) {
        galleryContainer.addEventListener('touchstart', function(e) {
            touchStartX = e.changedTouches[0].screenX;
        });

        galleryContainer.addEventListener('touchend', function(e) {
            touchEndX = e.changedTouches[0].screenX;
            handleSwipe();
        });
    }

    function handleSwipe() {
        const swipeThreshold = 50;
        const diff = touchStartX - touchEndX;

        if (Math.abs(diff) > swipeThreshold) {
            if (diff > 0 && nextBtn) {
                // Swipe left - next photo
                nextBtn.click();
            } else if (diff < 0 && prevBtn) {
                // Swipe right - previous photo
                prevBtn.click();
            }
        }
    }

    function updateMainPhoto() {
        if (galleryPhotos.length > 0) {
            const mainPhoto = document.querySelector('.main-photo');
            if (mainPhoto) {
                mainPhoto.src = galleryPhotos[currentPhotoIndex].url;
                mainPhoto.alt = galleryPhotos[currentPhotoIndex].alt;
                mainPhoto.dataset.photoIndex = currentPhotoIndex;
            }

            // Update counter
            const currentPhotoSpan = document.getElementById('current-photo');
            if (currentPhotoSpan) {
                currentPhotoSpan.textContent = currentPhotoIndex + 1;
            }
        }
    }

    function updateThumbnailActive() {
        document.querySelectorAll('.thumbnail').forEach((thumb, index) => {
            thumb.classList.toggle('active', index === currentPhotoIndex);
        });
    }

    function updateGalleryModal() {
        if (galleryPhotos.length > 0) {
            const modalImage = document.getElementById('gallery-main-image');
            if (modalImage) {
                modalImage.src = galleryPhotos[currentPhotoIndex].url;
                modalImage.alt = galleryPhotos[currentPhotoIndex].alt;
            }

            // Update modal counter
            const galleryCurrentSpan = document.getElementById('gallery-current');
            if (galleryCurrentSpan) {
                galleryCurrentSpan.textContent = currentPhotoIndex + 1;
            }
        }
    }

    // Initialize gallery modal when opened
    const galleryModal = document.getElementById('galleryModal');
    if (galleryModal) {
        galleryModal.addEventListener('show.bs.modal', function() {
            updateGalleryModal();
        });
    }
});

// Social sharing functionality
function copyToClipboard(text) {
    if (navigator.clipboard && window.isSecureContext) {
        // Use modern clipboard API
        navigator.clipboard.writeText(text).then(function() {
            showCopySuccess();
        }).catch(function(err) {
            fallbackCopyTextToClipboard(text);
        });
    } else {
        // Fallback for older browsers
        fallbackCopyTextToClipboard(text);
    }
}

function fallbackCopyTextToClipboard(text) {
    const textArea = document.createElement("textarea");
    textArea.value = text;
    textArea.style.top = "0";
    textArea.style.left = "0";
    textArea.style.position = "fixed";
    document.body.appendChild(textArea);
    textArea.focus();
    textArea.select();

    try {
        const successful = document.execCommand('copy');
        if (successful) {
            showCopySuccess();
        } else {
            showCopyError();
        }
    } catch (err) {
        showCopyError();
    }

    document.body.removeChild(textArea);
}

function showCopySuccess() {
    // Create and show success toast
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-success border-0 position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-check me-2"></i>Link copiat în clipboard!
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        document.body.removeChild(toast);
    });
}

function showCopyError() {
    // Create and show error toast
    const toast = document.createElement('div');
    toast.className = 'toast align-items-center text-white bg-danger border-0 position-fixed';
    toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999;';
    toast.setAttribute('role', 'alert');
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                <i class="fas fa-exclamation-triangle me-2"></i>Nu s-a putut copia link-ul
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
        </div>
    `;

    document.body.appendChild(toast);
    const bsToast = new bootstrap.Toast(toast);
    bsToast.show();

    // Remove toast after it's hidden
    toast.addEventListener('hidden.bs.toast', function() {
        document.body.removeChild(toast);
    });
}

// Track social sharing clicks for analytics (optional)
document.addEventListener('DOMContentLoaded', function() {
    const socialButtons = document.querySelectorAll('.social-btn');
    socialButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            const platform = this.textContent.trim();
            console.log(`Social share clicked: ${platform}`);
            // Here you could add analytics tracking
        });
    });
});
</script>
{% endblock %}

{% endblock %}
