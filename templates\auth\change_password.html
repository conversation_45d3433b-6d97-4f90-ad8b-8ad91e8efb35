{% extends 'base.html' %}
{% load static %}

{% block title %}<PERSON><PERSON><PERSON><PERSON> parola - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.change-password-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.form-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn-save {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-save:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 135, 84, 0.3);
}

.password-requirements {
    background: #f8f9fa;
    border-left: 4px solid #198754;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.password-requirements h6 {
    color: #198754;
    margin-bottom: 0.5rem;
}

.password-requirements ul {
    margin: 0;
    padding-left: 1.2rem;
}

.password-requirements li {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}

.security-info {
    background: #e7f3ff;
    border-left: 4px solid #0066cc;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.security-info h6 {
    color: #0066cc;
    margin-bottom: 0.5rem;
}

.security-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

@media (max-width: 768px) {
    .form-section {
        padding: 1rem;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="change-password-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1><i class="fas fa-key me-2"></i>Schimbă parola</h1>
                <p class="mb-0">Actualizează parola contului tău</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                {% endfor %}
            {% endif %}

            <div class="form-section">
                <form method="post" novalidate>
                    {% csrf_token %}
                    
                    <div class="form-floating mb-3">
                        {{ form.old_password }}
                        <label for="{{ form.old_password.id_for_label }}">Parola actuală*</label>
                        {% if form.old_password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.old_password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="password-requirements">
                        <h6><i class="fas fa-shield-alt me-2"></i>Cerințe pentru noua parolă:</h6>
                        <ul>
                            <li>Minimum 8 caractere</li>
                            <li>Nu poate fi similară cu informațiile personale</li>
                            <li>Nu poate fi o parolă comună</li>
                            <li>Nu poate conține doar cifre</li>
                        </ul>
                    </div>

                    <div class="form-floating mb-3">
                        {{ form.new_password1 }}
                        <label for="{{ form.new_password1.id_for_label }}">Parola nouă*</label>
                        {% if form.new_password1.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.new_password1.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-floating mb-3">
                        {{ form.new_password2 }}
                        <label for="{{ form.new_password2.id_for_label }}">Confirmă parola nouă*</label>
                        {% if form.new_password2.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.new_password2.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}

                    <div class="security-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Informații de securitate:</h6>
                        <p>După schimbarea parolei, veți rămâne autentificat pe acest dispozitiv. Pentru securitate maximă, vă recomandăm să vă deconectați de pe toate celelalte dispozitive.</p>
                    </div>

                    <div class="d-grid gap-2 mt-4">
                        <button type="submit" class="btn btn-success btn-save">
                            <i class="fas fa-save me-2"></i>Schimbă parola
                        </button>
                        <a href="{% url 'main:utilizator_profil' %}" class="btn btn-outline-secondary">
                            <i class="fas fa-times me-2"></i>Anulează
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating label animation
    const inputs = document.querySelectorAll('.form-floating input');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('filled');
        }
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
    });

    // Password strength indicator
    const newPassword1 = document.getElementById('{{ form.new_password1.id_for_label }}');
    const newPassword2 = document.getElementById('{{ form.new_password2.id_for_label }}');
    
    if (newPassword1) {
        newPassword1.addEventListener('input', function() {
            checkPasswordStrength(this.value);
        });
    }

    if (newPassword2) {
        newPassword2.addEventListener('input', function() {
            checkPasswordMatch();
        });
    }

    function checkPasswordStrength(password) {
        const requirements = document.querySelectorAll('.password-requirements li');
        
        // Check length
        if (requirements[0]) {
            if (password.length >= 8) {
                requirements[0].style.color = '#198754';
                requirements[0].innerHTML = '✓ Minimum 8 caractere';
            } else {
                requirements[0].style.color = '#6c757d';
                requirements[0].innerHTML = 'Minimum 8 caractere';
            }
        }
        
        // Check if not only numbers
        if (requirements[3]) {
            if (!/^\d+$/.test(password)) {
                requirements[3].style.color = '#198754';
                requirements[3].innerHTML = '✓ Nu poate conține doar cifre';
            } else {
                requirements[3].style.color = '#dc3545';
                requirements[3].innerHTML = '✗ Nu poate conține doar cifre';
            }
        }
    }

    function checkPasswordMatch() {
        if (newPassword1 && newPassword2) {
            if (newPassword1.value && newPassword2.value) {
                if (newPassword1.value === newPassword2.value) {
                    newPassword2.classList.remove('is-invalid');
                    newPassword2.classList.add('is-valid');
                } else {
                    newPassword2.classList.remove('is-valid');
                    newPassword2.classList.add('is-invalid');
                }
            }
        }
    }

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = form.querySelectorAll('input[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        // Check password match
        if (newPassword1 && newPassword2) {
            if (newPassword1.value !== newPassword2.value) {
                newPassword2.classList.add('is-invalid');
                isValid = false;
            }
        }
        
        if (!isValid) {
            e.preventDefault();
            // Scroll to first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });

    // Show/hide password toggle
    const passwordFields = document.querySelectorAll('input[type="password"]');
    passwordFields.forEach(field => {
        const wrapper = field.parentElement;
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary position-absolute';
        toggleBtn.style.cssText = 'right: 10px; top: 50%; transform: translateY(-50%); z-index: 10; border: none; background: none;';
        toggleBtn.innerHTML = '<i class="fas fa-eye"></i>';
        
        toggleBtn.addEventListener('click', function() {
            if (field.type === 'password') {
                field.type = 'text';
                this.innerHTML = '<i class="fas fa-eye-slash"></i>';
            } else {
                field.type = 'password';
                this.innerHTML = '<i class="fas fa-eye"></i>';
            }
        });
        
        wrapper.style.position = 'relative';
        wrapper.appendChild(toggleBtn);
    });
});
</script>
{% endblock %}
