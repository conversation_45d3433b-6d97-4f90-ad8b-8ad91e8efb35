{% extends 'base.html' %}
{% load static %}

{% block title %}Autentificare - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem;
    text-align: center;
}

.auth-body {
    padding: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn-login {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 135, 84, 0.3);
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

.auth-links a {
    color: #198754;
    text-decoration: none;
    font-weight: 500;
}

.auth-links a:hover {
    color: #20c997;
}

.login-benefits {
    background: #f8f9fa;
    border-left: 4px solid #198754;
    padding: 1.5rem;
    margin: 1.5rem 0;
    border-radius: 0 10px 10px 0;
}

.login-benefits h6 {
    color: #198754;
    margin-bottom: 1rem;
}

.benefit-item {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
}

.benefit-item i {
    color: #198754;
    margin-right: 0.75rem;
    width: 20px;
}

.benefit-item span {
    color: #6c757d;
    font-size: 0.95rem;
}

.remember-me {
    display: flex;
    align-items: center;
    margin: 1rem 0;
}

.remember-me input {
    margin-right: 0.5rem;
}

.remember-me label {
    margin: 0;
    color: #6c757d;
    font-size: 0.9rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container auth-container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="auth-card">
                <div class="auth-header">
                    <h2><i class="fas fa-sign-in-alt me-2"></i>Autentificare</h2>
                    <p class="mb-0">Bine ați revenit în comunitatea pescarilor</p>
                </div>
                
                <div class="auth-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="form-floating">
                            {{ form.username }}
                            <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-floating">
                            {{ form.password }}
                            <label for="{{ form.password.id_for_label }}">{{ form.password.label }}</label>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <div class="remember-me">
                            <input type="checkbox" id="remember-me" name="remember_me" class="form-check-input">
                            <label for="remember-me" class="form-check-label">Ține-mă minte</label>
                        </div>

                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-success btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>Autentifică-te
                            </button>
                        </div>
                    </form>

                    <div class="login-benefits">
                        <h6><i class="fas fa-star me-2"></i>Beneficiile contului:</h6>
                        <div class="benefit-item">
                            <i class="fas fa-plus-circle"></i>
                            <span>Adaugă și gestionează propriile balți</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-images"></i>
                            <span>Încarcă până la 10 fotografii per baltă</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-edit"></i>
                            <span>Editează informațiile balților tale</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-user-circle"></i>
                            <span>Profil personalizat de pescar</span>
                        </div>
                        <div class="benefit-item">
                            <i class="fas fa-share-alt"></i>
                            <span>Partajează balțile pe rețelele sociale</span>
                        </div>
                    </div>

                    <div class="auth-links">
                        <p class="mb-2">
                            Nu ai cont încă? 
                            <a href="{% url 'main:inregistrare' %}">
                                <i class="fas fa-user-plus me-1"></i>Înregistrează-te aici
                            </a>
                        </p>
                        <p class="mb-0">
                            <a href="#" class="text-muted">
                                <i class="fas fa-key me-1"></i>Ai uitat parola?
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating label animation
    const inputs = document.querySelectorAll('.form-floating input');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('filled');
        }
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
    });

    // Form validation feedback
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const inputs = form.querySelectorAll('input[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });

    // Remember me functionality
    const rememberCheckbox = document.getElementById('remember-me');
    if (localStorage.getItem('rememberMe') === 'true') {
        rememberCheckbox.checked = true;
        const savedUsername = localStorage.getItem('savedUsername');
        if (savedUsername) {
            document.getElementById('{{ form.username.id_for_label }}').value = savedUsername;
        }
    }

    form.addEventListener('submit', function() {
        if (rememberCheckbox.checked) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('savedUsername', document.getElementById('{{ form.username.id_for_label }}').value);
        } else {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('savedUsername');
        }
    });
});
</script>
{% endblock %}
