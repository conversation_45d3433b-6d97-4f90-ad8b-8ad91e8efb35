{% extends 'base.html' %}
{% load static %}

{% block title %}Autentificare - {{ block.super }}{% endblock %}

{% block content %}
<div class="modern-login-page">
    <div class="container-fluid h-100">
        <div class="row h-100">
            <!-- Left side - Image/Branding -->
            <div class="col-lg-6 d-none d-lg-flex login-left">
                <div class="login-branding">
                    <div class="brand-content">
                        <h1 class="brand-title">
                            <i class="fas fa-fish me-3"></i>
                            Răsfățul Pescarului
                        </h1>
                        <p class="brand-subtitle">Comunitatea pescarilor din România</p>
                        <div class="brand-features">
                            <div class="feature-item">
                                <i class="fas fa-map-marker-alt"></i>
                                <span>Peste 1000 de locații de pescuit</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-users"></i>
                                <span>Comunitate activă de pescari</span>
                            </div>
                            <div class="feature-item">
                                <i class="fas fa-star"></i>
                                <span>Recenzii și evaluări reale</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right side - Login Form -->
            <div class="col-lg-6 login-right">
                <div class="login-form-container">
                    <div class="login-header">
                        <div class="login-icon">
                            <i class="fas fa-sign-in-alt"></i>
                        </div>
                        <h2 class="login-title">Bine ai revenit!</h2>
                        <p class="login-subtitle">Conectează-te la contul tău</p>
                    </div>

                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate class="login-form">
                        {% csrf_token %}

                        <div class="form-group">
                            <label for="{{ form.username.id_for_label }}" class="form-label">
                                <i class="fas fa-user me-2"></i>Nume utilizator sau email
                            </label>
                            <input type="text"
                                   name="{{ form.username.name }}"
                                   id="{{ form.username.id_for_label }}"
                                   class="form-control modern-input"
                                   placeholder="Introdu numele de utilizator sau email-ul"
                                   value="{{ form.username.value|default:'' }}"
                                   required>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-group">
                            <label for="{{ form.password.id_for_label }}" class="form-label">
                                <i class="fas fa-lock me-2"></i>Parola
                            </label>
                            <div class="password-input-group">
                                <input type="password"
                                       name="{{ form.password.name }}"
                                       id="{{ form.password.id_for_label }}"
                                       class="form-control modern-input"
                                       placeholder="Introdu parola"
                                       required>
                                <button type="button" class="password-toggle-btn" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="toggleIcon"></i>
                                </button>
                            </div>
                            {% if form.password.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.non_field_errors %}
                            <div class="alert alert-danger">
                                {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}

                        <div class="form-options">
                            <div class="remember-me">
                                <input type="checkbox" id="remember-me" name="remember_me" class="form-check-input">
                                <label for="remember-me" class="form-check-label">Ține-mă minte</label>
                            </div>
                            <a href="#" class="forgot-password-link">Ai uitat parola?</a>
                        </div>

                        <button type="submit" class="btn btn-login w-100">
                            <i class="fas fa-sign-in-alt me-2"></i>Conectează-te
                        </button>
                    </form>

                    <div class="login-divider">
                        <span>sau</span>
                    </div>

                    <div class="register-link">
                        <p>Nu ai cont încă?
                            <a href="{% url 'main:inregistrare' %}" class="register-btn">
                                Înregistrează-te aici
                            </a>
                        </p>
                    </div>

                    <div class="benefits-preview">
                        <h6><i class="fas fa-star me-2"></i>Beneficiile contului:</h6>
                        <ul>
                            <li><i class="fas fa-plus-circle"></i>Adaugă propriile balți</li>
                            <li><i class="fas fa-images"></i>Încarcă fotografii</li>
                            <li><i class="fas fa-user-circle"></i>Profil personalizat</li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.modern-login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-left {
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.9), rgba(118, 75, 162, 0.9)),
                url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="fish" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="2" fill="rgba(255,255,255,0.1)"/></pattern></defs><rect width="100" height="100" fill="url(%23fish)"/></svg>');
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.login-branding {
    text-align: center;
    color: white;
    z-index: 2;
    position: relative;
}

.brand-title {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 1rem;
    text-shadow: 0 2px 10px rgba(0,0,0,0.3);
}

.brand-subtitle {
    font-size: 1.2rem;
    margin-bottom: 3rem;
    opacity: 0.9;
}

.brand-features {
    text-align: left;
    max-width: 300px;
    margin: 0 auto;
}

.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
    font-size: 1.1rem;
}

.feature-item i {
    width: 30px;
    margin-right: 1rem;
    font-size: 1.3rem;
}

.login-right {
    background: white;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
}

.login-form-container {
    width: 100%;
    max-width: 400px;
}

.login-header {
    text-align: center;
    margin-bottom: 2rem;
}

.login-icon {
    width: 70px;
    height: 70px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
}

.login-icon i {
    font-size: 1.8rem;
    color: white;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.login-subtitle {
    color: #718096;
    font-size: 1rem;
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-label {
    font-weight: 600;
    color: #4a5568;
    margin-bottom: 0.5rem;
    display: block;
}

.modern-input {
    border: 2px solid #e2e8f0;
    border-radius: 12px;
    padding: 0.875rem 1rem;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: #f7fafc;
}

.modern-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: white;
    outline: none;
}

.password-input-group {
    position: relative;
}

.password-toggle-btn {
    position: absolute;
    right: 12px;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 6px;
    transition: color 0.3s ease;
}

.password-toggle-btn:hover {
    color: #667eea;
}

.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
}

.remember-me {
    display: flex;
    align-items: center;
}

.remember-me input {
    margin-right: 0.5rem;
}

.forgot-password-link {
    color: #667eea;
    text-decoration: none;
    font-size: 0.9rem;
    font-weight: 500;
}

.forgot-password-link:hover {
    text-decoration: underline;
}

.btn-login {
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 12px;
    padding: 1rem 2rem;
    font-size: 1.1rem;
    font-weight: 600;
    color: white;
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.btn-login:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
    color: white;
}

.login-divider {
    text-align: center;
    position: relative;
    margin: 2rem 0;
}

.login-divider::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #e2e8f0;
}

.login-divider span {
    background: white;
    padding: 0 1rem;
    color: #a0aec0;
    font-weight: 500;
}

.register-link {
    text-align: center;
    margin-bottom: 2rem;
}

.register-btn {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
}

.register-btn:hover {
    text-decoration: underline;
}

.benefits-preview {
    background: #f7fafc;
    border-radius: 12px;
    padding: 1.5rem;
    border-left: 4px solid #667eea;
}

.benefits-preview h6 {
    color: #4a5568;
    margin-bottom: 1rem;
    font-weight: 600;
}

.benefits-preview ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-preview li {
    display: flex;
    align-items: center;
    margin-bottom: 0.75rem;
    color: #718096;
    font-size: 0.9rem;
}

.benefits-preview li i {
    color: #667eea;
    margin-right: 0.75rem;
    width: 16px;
}

@media (max-width: 991px) {
    .modern-login-page {
        padding: 2rem 0;
    }

    .login-right {
        min-height: 100vh;
    }
}

@media (max-width: 576px) {
    .login-right {
        padding: 1rem;
    }

    .brand-title {
        font-size: 2rem;
    }

    .login-form-container {
        max-width: none;
    }
}
</style>
{% endblock %}

{% block extra_js %}
<script>
// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.querySelector('.login-form');
    const inputs = document.querySelectorAll('.modern-input');
    const loginBtn = document.querySelector('.btn-login');

    // Enhanced input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
        });
    });

    // Form validation
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            if (input.hasAttribute('required') && !input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });

        if (!isValid) {
            e.preventDefault();
            return;
        }

        // Show loading state
        loginBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se conectează...';
        loginBtn.disabled = true;
    });

    // Remember me functionality
    const rememberCheckbox = document.getElementById('remember-me');
    const usernameInput = document.getElementById('{{ form.username.id_for_label }}');

    // Load saved credentials
    if (localStorage.getItem('rememberMe') === 'true') {
        rememberCheckbox.checked = true;
        const savedUsername = localStorage.getItem('savedUsername');
        if (savedUsername) {
            usernameInput.value = savedUsername;
        }
    }

    // Save credentials on form submit
    form.addEventListener('submit', function() {
        if (rememberCheckbox.checked) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('savedUsername', usernameInput.value);
        } else {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('savedUsername');
        }
    });

    // Auto-focus first empty input
    const firstEmptyInput = Array.from(inputs).find(input => !input.value.trim());
    if (firstEmptyInput) {
        firstEmptyInput.focus();
    }
});
</script>
{% endblock %}

{% block extra_js %}
<script>
// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const loginButton = document.getElementById('loginButton');
    const inputs = document.querySelectorAll('.form-input');

    // Enhanced input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim()) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });

        // Check if input has value on load
        if (input.value.trim()) {
            input.classList.add('filled');
        }
    });

    // Form validation with modern feedback
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            const value = input.value.trim();

            // Remove previous validation classes
            input.classList.remove('is-invalid', 'is-valid');

            if (input.hasAttribute('required') && !value) {
                input.classList.add('is-invalid');
                isValid = false;
            } else if (value) {
                input.classList.add('is-valid');
            }

            // Email validation for username field if it contains @
            if (input.name === '{{ form.username.name }}' && value.includes('@')) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    input.classList.add('is-invalid');
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            e.preventDefault();
            // Shake animation for invalid form
            form.classList.add('shake');
            setTimeout(() => form.classList.remove('shake'), 500);
            return;
        }

        // Show loading state
        loginButton.classList.add('loading');
        loginButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se conectează...';
        loginButton.disabled = true;
    });

    // Remember me functionality with enhanced UX
    const rememberCheckbox = document.getElementById('remember-me');
    const usernameInput = document.getElementById('{{ form.username.id_for_label }}');

    // Load saved credentials
    if (localStorage.getItem('rememberMe') === 'true') {
        rememberCheckbox.checked = true;
        const savedUsername = localStorage.getItem('savedUsername');
        if (savedUsername) {
            usernameInput.value = savedUsername;
            usernameInput.classList.add('filled');
        }
    }

    // Save credentials on form submit
    form.addEventListener('submit', function() {
        if (rememberCheckbox.checked) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('savedUsername', usernameInput.value);
        } else {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('savedUsername');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Enter key to submit form
        if (e.key === 'Enter' && !e.shiftKey) {
            const activeElement = document.activeElement;
            if (activeElement.classList.contains('form-input')) {
                e.preventDefault();
                form.submit();
            }
        }
    });

    // Auto-focus first empty input
    const firstEmptyInput = Array.from(inputs).find(input => !input.value.trim());
    if (firstEmptyInput) {
        firstEmptyInput.focus();
    }

    // Add shake animation CSS
    const style = document.createElement('style');
    style.textContent = `
        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .form-input.is-invalid {
            border-color: #f56565;
            box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.1);
        }

        .form-input.is-valid {
            border-color: #48bb78;
            box-shadow: 0 0 0 4px rgba(72, 187, 120, 0.1);
        }

        .form-group.focused .input-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
