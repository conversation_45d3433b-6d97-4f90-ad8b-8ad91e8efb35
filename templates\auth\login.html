{% extends 'base.html' %}
{% load static %}

{% block title %}Autentificare - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
/* Modern Login Page Styles */
.login-page {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
    overflow: hidden;
}

.login-page::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E") repeat;
    animation: float 20s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-20px); }
}

.login-container {
    position: relative;
    z-index: 2;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    padding: 2rem 1rem;
}

.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 24px;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.15),
        0 0 0 1px rgba(255, 255, 255, 0.2);
    overflow: hidden;
    width: 100%;
    max-width: 420px;
    position: relative;
}

.login-header {
    text-align: center;
    padding: 3rem 2rem 2rem;
    position: relative;
}

.login-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    box-shadow: 0 10px 30px rgba(102, 126, 234, 0.3);
    animation: pulse 2s ease-in-out infinite;
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.login-icon i {
    font-size: 2rem;
    color: white;
}

.login-title {
    font-size: 2rem;
    font-weight: 700;
    color: #2d3748;
    margin-bottom: 0.5rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.login-subtitle {
    color: #718096;
    font-size: 1rem;
    margin-bottom: 0;
}

.login-body {
    padding: 0 2rem 3rem;
}

.form-group {
    margin-bottom: 1.5rem;
    position: relative;
}

.form-input {
    width: 100%;
    padding: 1rem 1.25rem;
    border: 2px solid #e2e8f0;
    border-radius: 16px;
    font-size: 1rem;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
}

.form-input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
    background: white;
}

.form-input::placeholder {
    color: #a0aec0;
    font-weight: 500;
}

.input-icon {
    position: absolute;
    left: 1rem;
    top: 50%;
    transform: translateY(-50%);
    color: #a0aec0;
    font-size: 1.1rem;
    transition: color 0.3s ease;
}

.form-input:focus + .input-icon {
    color: #667eea;
}

.form-input.with-icon {
    padding-left: 3rem;
}

.password-toggle {
    position: absolute;
    right: 1rem;
    top: 50%;
    transform: translateY(-50%);
    background: none;
    border: none;
    color: #a0aec0;
    cursor: pointer;
    font-size: 1.1rem;
    transition: color 0.3s ease;
    z-index: 10;
}

.password-toggle:hover {
    color: #667eea;
}

.login-btn {
    width: 100%;
    padding: 1rem 2rem;
    background: linear-gradient(135deg, #667eea, #764ba2);
    border: none;
    border-radius: 16px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    margin-bottom: 1.5rem;
}

.login-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.6s;
}

.login-btn:hover::before {
    left: 100%;
}

.login-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(102, 126, 234, 0.4);
}

.login-btn:active {
    transform: translateY(-1px);
}

.benefits-section {
    background: linear-gradient(135deg, #f7fafc, #edf2f7);
    padding: 1.5rem;
    margin: 0 -2rem -3rem;
    border-radius: 0 0 24px 24px;
}

.benefits-title {
    font-size: 1rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.benefits-title i {
    margin-right: 0.5rem;
    color: #667eea;
}

.benefits-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.benefits-list li {
    display: flex;
    align-items: center;
    padding: 0.5rem 0;
    color: #4a5568;
    font-size: 0.9rem;
}

.benefits-list li i {
    margin-right: 0.75rem;
    color: #667eea;
    font-size: 1rem;
    width: 16px;
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #e2e8f0;
}

.auth-link {
    color: #667eea;
    text-decoration: none;
    font-weight: 600;
    transition: all 0.3s ease;
}

.auth-link:hover {
    color: #764ba2;
    text-decoration: underline;
}

.forgot-password {
    text-align: center;
    margin-top: 1rem;
}

.forgot-password a {
    color: #718096;
    text-decoration: none;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.forgot-password a:hover {
    color: #667eea;
}

.alert {
    border: none;
    border-radius: 12px;
    padding: 1rem 1.25rem;
    margin-bottom: 1.5rem;
    border-left: 4px solid;
    backdrop-filter: blur(10px);
}

.alert-success {
    background: rgba(72, 187, 120, 0.1);
    border-left-color: #48bb78;
    color: #2f855a;
}

.alert-danger {
    background: rgba(245, 101, 101, 0.1);
    border-left-color: #f56565;
    color: #c53030;
}

.remember-me {
    display: flex;
    align-items: center;
    margin-bottom: 1.5rem;
}

.remember-me input[type="checkbox"] {
    margin-right: 0.5rem;
    transform: scale(1.2);
    accent-color: #667eea;
}

.remember-me label {
    color: #4a5568;
    font-size: 0.9rem;
    cursor: pointer;
}

@media (max-width: 576px) {
    .login-container {
        padding: 1rem;
    }

    .login-header {
        padding: 2rem 1.5rem 1.5rem;
    }

    .login-body {
        padding: 0 1.5rem 2rem;
    }

    .login-title {
        font-size: 1.75rem;
    }

    .benefits-section {
        margin: 0 -1.5rem -2rem;
    }
}

/* Loading animation */
.login-btn.loading {
    pointer-events: none;
}

.login-btn.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid white;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Entrance animation */
.login-card {
    animation: slideInUp 0.8s ease-out;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %}

{% block content %}
<div class="login-page">
    <div class="login-container">
        <div class="login-card">
            <div class="login-header">
                <div class="login-icon">
                    <i class="fas fa-sign-in-alt"></i>
                </div>
                <h1 class="login-title">Autentificare</h1>
                <p class="login-subtitle">Bine ați revenit în comunitatea pescarilor</p>
            </div>

            <div class="login-body">
                {% if messages %}
                    {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    {% endfor %}
                {% endif %}

                <form method="post" novalidate id="loginForm">
                    {% csrf_token %}

                    <div class="form-group">
                        <input type="text"
                               name="{{ form.username.name }}"
                               id="{{ form.username.id_for_label }}"
                               class="form-input with-icon"
                               placeholder="Nume utilizator sau email"
                               value="{{ form.username.value|default:'' }}"
                               required>
                        <i class="fas fa-user input-icon"></i>
                        {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.username.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    <div class="form-group">
                        <input type="password"
                               name="{{ form.password.name }}"
                               id="{{ form.password.id_for_label }}"
                               class="form-input with-icon"
                               placeholder="Parola"
                               required>
                        <i class="fas fa-lock input-icon"></i>
                        <button type="button" class="password-toggle" onclick="togglePassword()">
                            <i class="fas fa-eye" id="toggleIcon"></i>
                        </button>
                        {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.password.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>

                    {% if form.non_field_errors %}
                        <div class="alert alert-danger">
                            {% for error in form.non_field_errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}

                    <div class="remember-me">
                        <input type="checkbox" id="remember-me" name="remember_me" class="form-check-input">
                        <label for="remember-me" class="form-check-label">Ține-mă minte</label>
                    </div>

                    <button type="submit" class="login-btn" id="loginButton">
                        <i class="fas fa-sign-in-alt me-2"></i>Autentifică-te
                    </button>
                </form>

                <div class="benefits-section">
                    <div class="benefits-title">
                        <i class="fas fa-star"></i>
                        Beneficiile contului:
                    </div>
                    <ul class="benefits-list">
                        <li><i class="fas fa-plus-circle"></i>Adaugă și gestionează propriile balți</li>
                        <li><i class="fas fa-images"></i>Încarcă până la 10 fotografii per baltă</li>
                        <li><i class="fas fa-edit"></i>Editează informațiile balților tale</li>
                        <li><i class="fas fa-user-circle"></i>Profil personalizat de pescar</li>
                        <li><i class="fas fa-share-alt"></i>Partajează balțile pe rețelele sociale</li>
                    </ul>
                </div>

                <div class="auth-links">
                    <p class="mb-2">
                        Nu ai cont încă?
                        <a href="{% url 'main:inregistrare' %}" class="auth-link">
                            <i class="fas fa-user-plus me-1"></i>Înregistrează-te aici
                        </a>
                    </p>
                </div>

                <div class="forgot-password">
                    <a href="#" class="text-muted">
                        <i class="fas fa-key me-1"></i>Ai uitat parola?
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// Password toggle functionality
function togglePassword() {
    const passwordInput = document.getElementById('{{ form.password.id_for_label }}');
    const toggleIcon = document.getElementById('toggleIcon');

    if (passwordInput.type === 'password') {
        passwordInput.type = 'text';
        toggleIcon.classList.remove('fa-eye');
        toggleIcon.classList.add('fa-eye-slash');
    } else {
        passwordInput.type = 'password';
        toggleIcon.classList.remove('fa-eye-slash');
        toggleIcon.classList.add('fa-eye');
    }
}

document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('loginForm');
    const loginButton = document.getElementById('loginButton');
    const inputs = document.querySelectorAll('.form-input');

    // Enhanced input focus effects
    inputs.forEach(input => {
        input.addEventListener('focus', function() {
            this.parentElement.classList.add('focused');
        });

        input.addEventListener('blur', function() {
            this.parentElement.classList.remove('focused');
            if (this.value.trim()) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });

        // Check if input has value on load
        if (input.value.trim()) {
            input.classList.add('filled');
        }
    });

    // Form validation with modern feedback
    form.addEventListener('submit', function(e) {
        let isValid = true;

        inputs.forEach(input => {
            const value = input.value.trim();

            // Remove previous validation classes
            input.classList.remove('is-invalid', 'is-valid');

            if (input.hasAttribute('required') && !value) {
                input.classList.add('is-invalid');
                isValid = false;
            } else if (value) {
                input.classList.add('is-valid');
            }

            // Email validation for username field if it contains @
            if (input.name === '{{ form.username.name }}' && value.includes('@')) {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(value)) {
                    input.classList.add('is-invalid');
                    isValid = false;
                }
            }
        });

        if (!isValid) {
            e.preventDefault();
            // Shake animation for invalid form
            form.classList.add('shake');
            setTimeout(() => form.classList.remove('shake'), 500);
            return;
        }

        // Show loading state
        loginButton.classList.add('loading');
        loginButton.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Se conectează...';
        loginButton.disabled = true;
    });

    // Remember me functionality with enhanced UX
    const rememberCheckbox = document.getElementById('remember-me');
    const usernameInput = document.getElementById('{{ form.username.id_for_label }}');

    // Load saved credentials
    if (localStorage.getItem('rememberMe') === 'true') {
        rememberCheckbox.checked = true;
        const savedUsername = localStorage.getItem('savedUsername');
        if (savedUsername) {
            usernameInput.value = savedUsername;
            usernameInput.classList.add('filled');
        }
    }

    // Save credentials on form submit
    form.addEventListener('submit', function() {
        if (rememberCheckbox.checked) {
            localStorage.setItem('rememberMe', 'true');
            localStorage.setItem('savedUsername', usernameInput.value);
        } else {
            localStorage.removeItem('rememberMe');
            localStorage.removeItem('savedUsername');
        }
    });

    // Keyboard shortcuts
    document.addEventListener('keydown', function(e) {
        // Enter key to submit form
        if (e.key === 'Enter' && !e.shiftKey) {
            const activeElement = document.activeElement;
            if (activeElement.classList.contains('form-input')) {
                e.preventDefault();
                form.submit();
            }
        }
    });

    // Auto-focus first empty input
    const firstEmptyInput = Array.from(inputs).find(input => !input.value.trim());
    if (firstEmptyInput) {
        firstEmptyInput.focus();
    }

    // Add shake animation CSS
    const style = document.createElement('style');
    style.textContent = `
        .shake {
            animation: shake 0.5s ease-in-out;
        }

        @keyframes shake {
            0%, 100% { transform: translateX(0); }
            25% { transform: translateX(-5px); }
            75% { transform: translateX(5px); }
        }

        .form-input.is-invalid {
            border-color: #f56565;
            box-shadow: 0 0 0 4px rgba(245, 101, 101, 0.1);
        }

        .form-input.is-valid {
            border-color: #48bb78;
            box-shadow: 0 0 0 4px rgba(72, 187, 120, 0.1);
        }

        .form-group.focused .input-icon {
            color: #667eea;
            transform: translateY(-50%) scale(1.1);
        }
    `;
    document.head.appendChild(style);
});
</script>
{% endblock %}
