{% extends 'base.html' %}
{% load static %}

{% block title %}Profilul meu - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.profile-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.profile-avatar {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    border: 4px solid white;
    object-fit: cover;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.profile-info h2 {
    margin-bottom: 0.5rem;
}

.profile-info p {
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.profile-stats {
    background: white;
    border-radius: 10px;
    padding: 1.5rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-top: -1rem;
    position: relative;
    z-index: 2;
}

.stat-item {
    text-align: center;
    padding: 1rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #198754;
    display: block;
}

.stat-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.profile-actions {
    margin: 2rem 0;
}

.btn-profile {
    margin: 0.25rem;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.3s ease;
}

.btn-profile:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.lakes-section {
    margin-top: 3rem;
}

.section-title {
    display: flex;
    align-items: center;
    justify-content: between;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e9ecef;
}

.section-title h3 {
    margin: 0;
    color: #198754;
}

.lake-card {
    background: white;
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    margin-bottom: 2rem;
}

.lake-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.lake-image {
    height: 200px;
    background-size: cover;
    background-position: center;
    position: relative;
}

.lake-image::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(to bottom, transparent, rgba(0, 0, 0, 0.3));
}

.lake-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    background: #198754;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 500;
    z-index: 2;
}

.lake-content {
    padding: 1.5rem;
}

.lake-title {
    font-size: 1.25rem;
    font-weight: bold;
    color: #333;
    margin-bottom: 0.5rem;
}

.lake-location {
    color: #6c757d;
    margin-bottom: 1rem;
    display: flex;
    align-items: center;
}

.lake-location i {
    margin-right: 0.5rem;
}

.lake-price {
    font-size: 1.1rem;
    font-weight: bold;
    color: #198754;
    margin-bottom: 1rem;
}

.lake-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-lake-action {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    text-decoration: none;
    transition: all 0.3s ease;
}

.no-lakes {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.no-lakes i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.pagination-wrapper {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.last-updated {
    font-size: 0.85rem;
    color: #6c757d;
    margin-top: 0.5rem;
}
</style>
{% endblock %}

{% block content %}
<div class="profile-header">
    <div class="container">
        <div class="row align-items-center">
            <div class="col-md-3 text-center">
                {% if profile.avatar %}
                    <img src="{{ profile.avatar.url }}" alt="Avatar" class="profile-avatar">
                {% else %}
                    <div class="profile-avatar d-flex align-items-center justify-content-center" style="background: rgba(255,255,255,0.2);">
                        <i class="fas fa-user fa-3x"></i>
                    </div>
                {% endif %}
            </div>
            <div class="col-md-9">
                <div class="profile-info">
                    <h2>{{ profile.get_full_name }}</h2>
                    <p><i class="fas fa-envelope me-2"></i>{{ user.email }}</p>
                    {% if profile.phone %}
                        <p><i class="fas fa-phone me-2"></i>{{ profile.phone }}</p>
                    {% endif %}
                    {% if profile.city %}
                        <p><i class="fas fa-map-marker-alt me-2"></i>{{ profile.city }}{% if profile.county %}, {{ profile.county.name }}{% endif %}</p>
                    {% endif %}
                    <p><i class="fas fa-calendar me-2"></i>Membru din {{ profile.created_at|date:"F Y" }}</p>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container">
    <div class="profile-stats">
        <div class="row">
            <div class="col-md-4">
                <div class="stat-item">
                    <span class="stat-number">{{ total_lakes }}</span>
                    <span class="stat-label">Balți adăugate</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <span class="stat-number">{{ user.date_joined|timesince|truncatewords:1 }}</span>
                    <span class="stat-label">Vechime</span>
                </div>
            </div>
            <div class="col-md-4">
                <div class="stat-item">
                    <span class="stat-number">{{ profile.updated_at|date:"d.m.Y" }}</span>
                    <span class="stat-label">Ultima actualizare</span>
                </div>
            </div>
        </div>
    </div>

    <div class="profile-actions text-center">
        <a href="{% url 'main:editare_profil' %}" class="btn btn-outline-success btn-profile">
            <i class="fas fa-edit me-2"></i>Editează profilul
        </a>
        <a href="{% url 'main:schimbare_parola' %}" class="btn btn-outline-secondary btn-profile">
            <i class="fas fa-key me-2"></i>Schimbă parola
        </a>
        <a href="{% url 'main:creaza_balta' %}" class="btn btn-success btn-profile">
            <i class="fas fa-plus me-2"></i>Adaugă baltă nouă
        </a>
        <a href="{% url 'main:baltile_mele' %}" class="btn btn-outline-primary btn-profile">
            <i class="fas fa-list me-2"></i>Toate balțile mele
        </a>
    </div>

    {% if profile.bio %}
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <h5 class="card-title"><i class="fas fa-user-circle me-2"></i>Despre mine</h5>
                        <p class="card-text">{{ profile.bio|linebreaks }}</p>
                    </div>
                </div>
            </div>
        </div>
    {% endif %}

    <div class="lakes-section">
        <div class="section-title">
            <h3><i class="fas fa-water me-2"></i>Balțile mele recente</h3>
            {% if total_lakes > 6 %}
                <a href="{% url 'main:baltile_mele' %}" class="btn btn-outline-success btn-sm">
                    Vezi toate ({{ total_lakes }})
                </a>
            {% endif %}
        </div>

        {% if lakes_page.object_list %}
            <div class="row">
                {% for lake in lakes_page.object_list %}
                    <div class="col-lg-4 col-md-6">
                        <div class="lake-card">
                            <div class="lake-image" 
                                 style="background-image: url('{% if lake.get_main_photo %}{{ lake.get_main_photo.url }}{% else %}{% static 'images/lake-placeholder.jpg' %}{% endif %}');">
                                <div class="lake-badge">{{ lake.get_lake_type_display }}</div>
                            </div>
                            <div class="lake-content">
                                <h5 class="lake-title">{{ lake.name }}</h5>
                                <div class="lake-location">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {{ lake.address }}
                                </div>
                                <div class="lake-price">{{ lake.price_per_day }} RON/zi</div>
                                <div class="last-updated">
                                    <i class="fas fa-clock me-1"></i>
                                    Ultima actualizare: {{ lake.get_last_updated_display }}
                                </div>
                                <div class="lake-actions mt-3">
                                    <a href="{{ lake.get_absolute_url }}" class="btn btn-outline-success btn-lake-action">
                                        <i class="fas fa-eye me-1"></i>Vezi
                                    </a>
                                    <a href="{% url 'main:editeaza_balta' lake.slug %}" class="btn btn-outline-primary btn-lake-action">
                                        <i class="fas fa-edit me-1"></i>Editează
                                    </a>
                                    <a href="{% url 'main:manage_lake_photos' lake.slug %}" class="btn btn-outline-info btn-lake-action">
                                        <i class="fas fa-images me-1"></i>Fotografii
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                {% endfor %}
            </div>

            {% if lakes_page.has_other_pages %}
                <div class="pagination-wrapper">
                    <nav aria-label="Navigare balți">
                        <ul class="pagination">
                            {% if lakes_page.has_previous %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ lakes_page.previous_page_number }}">Anterior</a>
                                </li>
                            {% endif %}
                            
                            {% for num in lakes_page.paginator.page_range %}
                                {% if lakes_page.number == num %}
                                    <li class="page-item active">
                                        <span class="page-link">{{ num }}</span>
                                    </li>
                                {% else %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if lakes_page.has_next %}
                                <li class="page-item">
                                    <a class="page-link" href="?page={{ lakes_page.next_page_number }}">Următor</a>
                                </li>
                            {% endif %}
                        </ul>
                    </nav>
                </div>
            {% endif %}
        {% else %}
            <div class="no-lakes">
                <i class="fas fa-water"></i>
                <h4>Nu aveți balți adăugate încă</h4>
                <p>Începeți prin a adăuga prima dvs. baltă de pescuit!</p>
                <a href="{% url 'main:creaza_balta' %}" class="btn btn-success btn-lg">
                    <i class="fas fa-plus me-2"></i>Adaugă prima baltă
                </a>
            </div>
        {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add smooth scrolling to section links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            e.preventDefault();
            document.querySelector(this.getAttribute('href')).scrollIntoView({
                behavior: 'smooth'
            });
        });
    });

    // Add loading states to action buttons
    document.querySelectorAll('.btn-lake-action').forEach(button => {
        button.addEventListener('click', function() {
            if (!this.classList.contains('btn-outline-success')) {
                this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Se încarcă...';
            }
        });
    });
});
</script>
{% endblock %}
