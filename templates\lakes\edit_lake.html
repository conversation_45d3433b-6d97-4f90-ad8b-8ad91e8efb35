{% extends 'base.html' %}
{% load static %}

{% block title %}Editează {{ lake.name }} - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.edit-lake-header {
    background: linear-gradient(135deg, #0d6efd, #0b5ed7);
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.form-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    padding: 2rem;
    margin-bottom: 2rem;
}

.section-title {
    color: #0d6efd;
    border-bottom: 2px solid #e9ecef;
    padding-bottom: 0.5rem;
    margin-bottom: 1.5rem;
}

.required-field {
    color: #dc3545;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus, .form-select:focus {
    border-color: #0d6efd;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.25);
}

.checkbox-group {
    max-height: 300px;
    overflow-y: auto;
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    padding: 1rem;
    background: #f8f9fa;
}

.checkbox-item {
    margin-bottom: 0.5rem;
}

.checkbox-item:last-child {
    margin-bottom: 0;
}

.form-check-input:checked {
    background-color: #0d6efd;
    border-color: #0d6efd;
}

.btn-update {
    background: linear-gradient(135deg, #0d6efd, #0b5ed7);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-update:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(13, 110, 253, 0.3);
}

.lake-info {
    background: #e7f3ff;
    border-left: 4px solid #0d6efd;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.lake-info h6 {
    color: #0d6efd;
    margin-bottom: 0.5rem;
}

.lake-info p {
    margin: 0;
    font-size: 0.9rem;
    color: #6c757d;
}

.coordinates-help {
    background: #e7f3ff;
    border-left: 4px solid #0066cc;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.coordinates-help h6 {
    color: #0066cc;
    margin-bottom: 0.5rem;
}

.coordinates-help ol {
    margin: 0;
    padding-left: 1.2rem;
}

.coordinates-help li {
    margin-bottom: 0.25rem;
    font-size: 0.9rem;
}

@media (max-width: 768px) {
    .form-section {
        padding: 1rem;
    }
    
    .checkbox-group {
        max-height: 200px;
    }
}
</style>
{% endblock %}

{% block content %}
<div class="edit-lake-header">
    <div class="container">
        <div class="row">
            <div class="col-12 text-center">
                <h1><i class="fas fa-edit me-2"></i>Editează balta</h1>
                <p class="mb-0">Actualizează informațiile despre "{{ lake.name }}"</p>
            </div>
        </div>
    </div>
</div>

<div class="container">
    {% if messages %}
        {% for message in messages %}
            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                {{ message }}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        {% endfor %}
    {% endif %}

    <div class="lake-info">
        <h6><i class="fas fa-info-circle me-2"></i>Informații baltă</h6>
        <p>
            <strong>Creată:</strong> {{ lake.created_at|date:"d.m.Y H:i" }} | 
            <strong>Ultima actualizare:</strong> {{ lake.updated_at|date:"d.m.Y H:i" }} |
            <strong>Fotografii:</strong> {{ lake.photos.count }}
        </p>
    </div>

    <form method="post" novalidate>
        {% csrf_token %}
        
        <!-- Basic Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-info-circle me-2"></i>Informații de bază
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-floating mb-3">
                        {{ form.name }}
                        <label for="{{ form.name.id_for_label }}">{{ form.name.label }}</label>
                        {% if form.name.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.name.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-floating mb-3">
                        {{ form.lake_type }}
                        <label for="{{ form.lake_type.id_for_label }}">{{ form.lake_type.label }}</label>
                        {% if form.lake_type.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.lake_type.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.description.id_for_label }}" class="form-label">{{ form.description.label }}</label>
                {{ form.description }}
                {% if form.description.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.description.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Location Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-map-marker-alt me-2"></i>Locație
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-8">
                    <div class="form-floating mb-3">
                        {{ form.address }}
                        <label for="{{ form.address.id_for_label }}">{{ form.address.label }}</label>
                        {% if form.address.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.address.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-4">
                    <div class="form-floating mb-3">
                        {{ form.county }}
                        <label for="{{ form.county.id_for_label }}">{{ form.county.label }}</label>
                        {% if form.county.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.county.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="coordinates-help">
                <h6><i class="fas fa-info-circle me-2"></i>Cum să găsești coordonatele:</h6>
                <ol>
                    <li>Deschide <a href="https://maps.google.com" target="_blank">Google Maps</a></li>
                    <li>Caută locația bălții tale</li>
                    <li>Fă click dreapta pe locația exactă</li>
                    <li>Copiază coordonatele din primul rând (ex: 45.39189813235069, 24.62707585690222)</li>
                </ol>
            </div>

            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.latitude }}
                        <label for="{{ form.latitude.id_for_label }}">{{ form.latitude.label }}</label>
                        {% if form.latitude.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.latitude.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.longitude }}
                        <label for="{{ form.longitude.id_for_label }}">{{ form.longitude.label }}</label>
                        {% if form.longitude.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.longitude.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.google_maps_embed.id_for_label }}" class="form-label">{{ form.google_maps_embed.label }}</label>
                {{ form.google_maps_embed }}
                {% if form.google_maps_embed.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.google_maps_embed.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Fish Species & Facilities -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-fish me-2"></i>Specii de pești și facilități
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <label class="form-label">{{ form.fish_species.label }}</label>
                    <div class="checkbox-group">
                        {{ form.fish_species }}
                    </div>
                    {% if form.fish_species.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.fish_species.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
                <div class="col-md-6">
                    <label class="form-label">{{ form.facilities.label }}</label>
                    <div class="checkbox-group">
                        {{ form.facilities }}
                    </div>
                    {% if form.facilities.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in form.facilities.errors %}{{ error }}{% endfor %}
                        </div>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- Pricing & Rules -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-euro-sign me-2"></i>Preț și regulament
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.price_per_day }}
                        <label for="{{ form.price_per_day.id_for_label }}">{{ form.price_per_day.label }}</label>
                        {% if form.price_per_day.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.price_per_day.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>

            <div class="mb-3">
                <label for="{{ form.rules.id_for_label }}" class="form-label">{{ form.rules.label }}</label>
                {{ form.rules }}
                {% if form.rules.errors %}
                    <div class="invalid-feedback d-block">
                        {% for error in form.rules.errors %}{{ error }}{% endfor %}
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Contact Information -->
        <div class="form-section">
            <h3 class="section-title">
                <i class="fas fa-phone me-2"></i>Date de contact
                <span class="required-field">*</span>
            </h3>
            
            <div class="row">
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.contact_phone }}
                        <label for="{{ form.contact_phone.id_for_label }}">{{ form.contact_phone.label }}</label>
                        {% if form.contact_phone.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.contact_phone.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="form-floating mb-3">
                        {{ form.contact_email }}
                        <label for="{{ form.contact_email.id_for_label }}">{{ form.contact_email.label }}</label>
                        {% if form.contact_email.errors %}
                            <div class="invalid-feedback d-block">
                                {% for error in form.contact_email.errors %}{{ error }}{% endfor %}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>

        <!-- Action Buttons -->
        <div class="text-center mb-4">
            <button type="submit" class="btn btn-primary btn-update btn-lg">
                <i class="fas fa-save me-2"></i>Actualizează balta
            </button>
            <a href="{{ lake.get_absolute_url }}" class="btn btn-outline-secondary btn-lg ms-3">
                <i class="fas fa-times me-2"></i>Anulează
            </a>
            <a href="{% url 'main:manage_lake_photos' lake.slug %}" class="btn btn-outline-info btn-lg ms-2">
                <i class="fas fa-images me-2"></i>Gestionează fotografii
            </a>
        </div>
    </form>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating label animation
    const inputs = document.querySelectorAll('.form-floating input, .form-floating select, .form-floating textarea');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('filled');
        }
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
    });

    // Form validation
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        let isValid = true;
        
        // Check required fields
        const requiredFields = form.querySelectorAll('input[required], select[required], textarea[required]');
        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.classList.add('is-invalid');
                isValid = false;
            } else {
                field.classList.remove('is-invalid');
            }
        });
        
        // Check fish species
        const fishSpeciesChecked = form.querySelectorAll('input[name="fish_species"]:checked');
        if (fishSpeciesChecked.length === 0) {
            alert('Vă rugăm să selectați cel puțin o specie de pește.');
            isValid = false;
        }
        
        // Check facilities
        const facilitiesChecked = form.querySelectorAll('input[name="facilities"]:checked');
        if (facilitiesChecked.length === 0) {
            alert('Vă rugăm să selectați cel puțin o facilitate.');
            isValid = false;
        }
        
        if (!isValid) {
            e.preventDefault();
            // Scroll to first error
            const firstError = form.querySelector('.is-invalid');
            if (firstError) {
                firstError.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
    });

    // Auto-format coordinates
    const latInput = document.getElementById('{{ form.latitude.id_for_label }}');
    const lngInput = document.getElementById('{{ form.longitude.id_for_label }}');
    
    [latInput, lngInput].forEach(input => {
        if (input) {
            input.addEventListener('blur', function() {
                const value = this.value.trim();
                if (value && !isNaN(value)) {
                    // Format to high precision
                    this.value = parseFloat(value).toFixed(15);
                }
            });
        }
    });

    // Track form changes
    let formChanged = false;
    const formElements = form.querySelectorAll('input, select, textarea');
    formElements.forEach(element => {
        element.addEventListener('change', function() {
            formChanged = true;
        });
    });

    // Warn before leaving if form has changes
    window.addEventListener('beforeunload', function(e) {
        if (formChanged) {
            e.preventDefault();
            e.returnValue = '';
        }
    });

    // Don't warn when submitting
    form.addEventListener('submit', function() {
        formChanged = false;
    });
});
</script>
{% endblock %}
