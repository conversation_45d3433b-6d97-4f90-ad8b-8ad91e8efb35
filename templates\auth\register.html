{% extends 'base.html' %}
{% load static %}

{% block title %}Înregistrare - {{ block.super }}{% endblock %}

{% block extra_css %}
<style>
.auth-container {
    min-height: 80vh;
    display: flex;
    align-items: center;
}

.auth-card {
    background: white;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.auth-header {
    background: linear-gradient(135deg, #198754, #20c997);
    color: white;
    padding: 2rem;
    text-align: center;
}

.auth-body {
    padding: 2rem;
}

.form-floating {
    margin-bottom: 1rem;
}

.form-floating label {
    color: #6c757d;
}

.form-control:focus {
    border-color: #198754;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.25);
}

.btn-register {
    background: linear-gradient(135deg, #198754, #20c997);
    border: none;
    padding: 0.75rem 2rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    transition: all 0.3s ease;
}

.btn-register:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(25, 135, 84, 0.3);
}

.auth-links {
    text-align: center;
    margin-top: 1.5rem;
    padding-top: 1.5rem;
    border-top: 1px solid #dee2e6;
}

.auth-links a {
    color: #198754;
    text-decoration: none;
    font-weight: 500;
}

.auth-links a:hover {
    color: #20c997;
}

.password-requirements {
    background: #f8f9fa;
    border-left: 4px solid #198754;
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 0 5px 5px 0;
}

.password-requirements h6 {
    color: #198754;
    margin-bottom: 0.5rem;
}

.password-requirements ul {
    margin: 0;
    padding-left: 1.2rem;
}

.password-requirements li {
    font-size: 0.9rem;
    color: #6c757d;
    margin-bottom: 0.25rem;
}
</style>
{% endblock %}

{% block content %}
<div class="container auth-container">
    <div class="row justify-content-center">
        <div class="col-md-8 col-lg-6">
            <div class="auth-card">
                <div class="auth-header">
                    <h2><i class="fas fa-user-plus me-2"></i>Creează cont nou</h2>
                    <p class="mb-0">Alătură-te comunității pescarilor din România</p>
                </div>
                
                <div class="auth-body">
                    {% if messages %}
                        {% for message in messages %}
                            <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                                {{ message }}
                                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                            </div>
                        {% endfor %}
                    {% endif %}

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ form.first_name }}
                                    <label for="{{ form.first_name.id_for_label }}">{{ form.first_name.label }}</label>
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.first_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                            <div class="col-md-6">
                                <div class="form-floating">
                                    {{ form.last_name }}
                                    <label for="{{ form.last_name.id_for_label }}">{{ form.last_name.label }}</label>
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            {% for error in form.last_name.errors %}{{ error }}{% endfor %}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-floating">
                            {{ form.username }}
                            <label for="{{ form.username.id_for_label }}">{{ form.username.label }}</label>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.username.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-floating">
                            {{ form.email }}
                            <label for="{{ form.email.id_for_label }}">{{ form.email.label }}</label>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.email.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="password-requirements">
                            <h6><i class="fas fa-shield-alt me-2"></i>Cerințe parolă:</h6>
                            <ul>
                                <li>Minimum 8 caractere</li>
                                <li>Nu poate fi similară cu informațiile personale</li>
                                <li>Nu poate fi o parolă comună</li>
                                <li>Nu poate conține doar cifre</li>
                            </ul>
                        </div>

                        <div class="form-floating">
                            {{ form.password1 }}
                            <label for="{{ form.password1.id_for_label }}">{{ form.password1.label }}</label>
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password1.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-floating">
                            {{ form.password2 }}
                            <label for="{{ form.password2.id_for_label }}">{{ form.password2.label }}</label>
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {% for error in form.password2.errors %}{{ error }}{% endfor %}
                                </div>
                            {% endif %}
                        </div>

                        <div class="d-grid gap-2 mt-3">
                            <button type="submit" class="btn btn-success btn-register">
                                <i class="fas fa-user-plus me-2"></i>Creează contul
                            </button>
                        </div>
                    </form>

                    <div class="auth-links">
                        <p class="mb-0">
                            Ai deja cont? 
                            <a href="{% url 'main:autentificare' %}">
                                <i class="fas fa-sign-in-alt me-1"></i>Autentifică-te aici
                            </a>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add floating label animation
    const inputs = document.querySelectorAll('.form-floating input');
    inputs.forEach(input => {
        if (input.value) {
            input.classList.add('filled');
        }
        
        input.addEventListener('blur', function() {
            if (this.value) {
                this.classList.add('filled');
            } else {
                this.classList.remove('filled');
            }
        });
    });

    // Form validation feedback
    const form = document.querySelector('form');
    form.addEventListener('submit', function(e) {
        const inputs = form.querySelectorAll('input[required]');
        let isValid = true;
        
        inputs.forEach(input => {
            if (!input.value.trim()) {
                input.classList.add('is-invalid');
                isValid = false;
            } else {
                input.classList.remove('is-invalid');
            }
        });
        
        if (!isValid) {
            e.preventDefault();
        }
    });
});
</script>
{% endblock %}
